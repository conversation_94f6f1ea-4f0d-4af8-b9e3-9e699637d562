export default (function () {
  function t() {
    this.name = 'fontSize'; // 重写的参数 key
  }

  // 涉及修改元素样式， 添加一个 css 方法
  t.prototype.css = function (t, e) {
    if (t && t.length) {
      if (e) return t.css('font-size', e + 'pt'), 'font-size:' + e + 'pt';
      t[0].style.fontSize = '';
    }
    return null;
  };

  // 创建 DOM - 使用输入框而不是下拉选择
  t.prototype.createTarget = function () {
    // 预设字体大小选项
    let presetSizes = [
      8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 40, 44, 48,
      54, 60, 72, 84, 96, 108, 120
    ];
    let optionsList = '<option value="">默认</option>';
    presetSizes.forEach(function (size) {
      optionsList += '<option value="' + size + '">' + size + 'pt</option>';
    });

    this.target = $(`
      <div class="hiprint-option-item">
        <div class="hiprint-option-item-label">字体大小</div>
        <div class="hiprint-option-item-field">
          <div style="display: flex; align-items: center;">
            <select class="auto-submit font-size-select" style="width: 80px; margin-right: 5px;">
              ${optionsList}
            </select>
            <input type="number" class="font-size-input" 
                   style="width: 60px; height: 22px; border: 1px solid #ccc; padding: 2px;" 
                   placeholder="自定义" min="1" max="999" step="0.25" />
            <span style="margin-left: 2px; font-size: 12px;">pt</span>
          </div>
        </div>
      </div>
    `);

    // 绑定事件
    const selectElement = this.target.find('.font-size-select');
    const inputElement = this.target.find('.font-size-input');

    // 下拉选择事件
    selectElement.on('change', function () {
      const value = $(this).val();
      if (value) {
        inputElement.val(value);
        inputElement.trigger('change');
      }
    });

    // 输入框事件
    inputElement.on('input change', function () {
      const value = parseFloat($(this).val());
      if (value && value > 0 && value <= 999) {
        selectElement.val(''); // 清空下拉选择
        // 触发hiprint的更新事件
        if (
          typeof window.hiprintTemplate !== 'undefined' &&
          window.hiprintTemplate
        ) {
          window.hiprintTemplate.updateOption('fontSize', value);
        }
      }
    });

    return this.target;
  };

  // 获取值
  t.prototype.getValue = function () {
    const selectVal = this.target.find('.font-size-select').val();
    const inputVal = this.target.find('.font-size-input').val();

    if (inputVal && parseFloat(inputVal) > 0) {
      return parseFloat(inputVal);
    } else if (selectVal) {
      return parseFloat(selectVal);
    }
    return null;
  };

  // 设置值
  t.prototype.setValue = function (value) {
    if (value) {
      const selectElement = this.target.find('.font-size-select');
      const inputElement = this.target.find('.font-size-input');

      // 检查预设值中是否存在
      if (selectElement.find('option[value="' + value + '"]').length > 0) {
        selectElement.val(value);
        inputElement.val('');
      } else {
        // 使用自定义输入
        selectElement.val('');
        inputElement.val(value);
      }
    }
  };

  // 销毁 DOM
  t.prototype.destroy = function () {
    this.target.remove();
  };

  return t;
})();
