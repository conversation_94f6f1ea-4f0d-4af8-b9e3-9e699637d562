<template>
  <el-dialog
    v-model="visible"
    title="增加钱币"
    width="90%"
    @close="handleClose"
  >
    <div class="dialog-content">
      <div class="sendform-info">
        <h4>录入该送评单号内：{{ sendnum }}</h4>
      </div>

      <!-- 钱币基本信息 -->
      <el-form ref="formRef" :model="form" label-width="120px" size="small">
        <div class="section-title">
          <h5>钱币基本信息</h5>
        </div>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="钱币类型" prop="coinType" required>
              <el-input
                v-model="coinTypeDisplay"
                disabled
                placeholder="钱币类型"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <!-- 通用字段 -->
          <el-col :span="8">
            <el-form-item label="送评条码">
              <el-input v-model="form.diyCode" placeholder="请输入送评条码" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="名称1">
              <el-input v-model="form.coinName1" placeholder="请输入名称1" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="名称2">
              <el-input v-model="form.coinName2" placeholder="请输入名称2" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="年代">
              <el-input v-model="form.yearInfo" placeholder="请输入年代" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="版别">
              <el-select
                v-model="form.coinVersion"
                placeholder="请选择版别"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="item in editionOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 根据钱币类型显示不同字段 -->
        <dynamic-coin-fields :coin-type="existingCoinType" :form="form" />

        <!-- 真伪鉴定 -->
        <div class="section-title">
          <h5>真伪鉴定（存档）</h5>
        </div>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="真伪">
              <el-select
                v-model="form.authenticity"
                placeholder="请选择真伪"
                style="width: 100%"
              >
                <el-option
                  v-for="item in authenticityOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="钱币备注">
              <el-select
                v-model="form.coinRemark"
                placeholder="请选择钱币备注"
                style="width: 100%"
              >
                <el-option
                  v-for="item in coinRemarkOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 评级打分 -->
        <div class="section-title">
          <h5>评级打分（存档）</h5>
        </div>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="品相分数">
              <el-select
                v-model="form.gradeScore"
                placeholder="请选择品相分数"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="item in gradeScoreOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="特殊标记">
              <el-select
                v-model="form.specialMarks"
                placeholder="请选择特殊标记"
                multiple
                style="width: 100%"
              >
                <el-option
                  v-for="item in specialMarkOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="星级">
              <el-select
                v-model="form.starLevel"
                placeholder="请选择星级"
                style="width: 100%"
              >
                <el-option
                  v-for="item in starLevelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 验货标注 -->
        <div class="section-title">
          <h5>验货标注（存档）</h5>
        </div>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="对内备注">
              <el-input
                v-model="form.internalNote"
                placeholder="对内备注"
                type="textarea"
                :rows="2"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="对外备注">
              <el-input
                v-model="form.externalNote"
                placeholder="对外备注"
                type="textarea"
                :rows="2"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 费用信息 -->
        <div class="section-title">
          <h5>费用信息</h5>
        </div>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="标准价">
              <el-input-number
                v-model="form.standardPrice"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="国际价">
              <el-input-number
                v-model="form.internationalPrice"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="评级费">
              <el-input-number
                v-model="form.gradeFee"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="折扣">
              <el-input-number
                v-model="form.discount"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="盒子费">
              <el-input-number
                v-model="form.boxFee"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="数量">
              <el-input-number
                v-model="form.quantity"
                :min="1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 图片上传区域 -->
        <div class="section-title">
          <h5>图片</h5>
        </div>

        <div class="image-upload">
          <coin-image-upload
            v-model="form.coinImages"
            :limit="5"
            :show-title="false"
            placeholder="点击或拖拽上传钱币图片"
            :max-size="5"
            @upload-success="onImageUploadSuccess"
            @remove-success="onImageRemoveSuccess"
          />
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="saving">
          保存并添加
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
  import {
    defineComponent,
    ref,
    reactive,
    watch,
    computed,
    onMounted
  } from 'vue';
  import { ElMessage } from 'element-plus';
  import { addCoins, getSendformDetail } from '../api';
  import DictData from '@/components/DictData/index.vue';
  import DynamicCoinFields from './DynamicCoinFields.vue';
  import CoinImageUpload from '@/components/CoinImageUpload/index.vue';
  import {
    getEditionOptions,
    GRADE_SCORE_OPTIONS,
    SPECIAL_MARK_OPTIONS,
    getAuthenticityOptions,
    COIN_REMARK_OPTIONS,
    STAR_LEVEL_OPTIONS,
    BOX_TYPE_OPTIONS
  } from '@/config/coin-dict-options';

  export default defineComponent({
    name: 'AddCoinDialog',
    components: {
      DictData,
      DynamicCoinFields,
      CoinImageUpload
    },
    props: {
      modelValue: {
        type: Boolean,
        default: false
      },
      sendnum: {
        type: String,
        default: ''
      }
    },
    emits: ['confirm', 'update:modelValue', 'done'],
    setup(props, { emit }) {
      const formRef = ref();
      const saving = ref(false);

      // 使用计算属性来响应 props 变化
      const visible = computed({
        get: () => props.modelValue,
        set: (value) => emit('update:modelValue', value)
      });

      // 使用计算属性获取 sendnum
      const sendnum = computed(() => props.sendnum);

      // 钱币类型相关
      const existingCoinType = ref('');
      const loading = ref(false);

      /** 钱币类型中英文映射 */
      const coinTypeMap = {
        banknote: '纸币',
        ancientCoin: '古钱币',
        machineCoin: '机制币',
        silverIngot: '银锭',
        纸币: '纸币',
        古钱币: '古钱币',
        机制币: '机制币',
        银锭: '银锭'
      };

      /** 钱币类型中文显示 */
      const coinTypeDisplay = computed(() => {
        return (
          coinTypeMap[existingCoinType.value] ||
          existingCoinType.value ||
          '未确定'
        );
      });

      // 监听 sendnum 变化，自动加载送评单详情
      watch(
        () => props.sendnum,
        (newSendnum) => {
          if (newSendnum && props.modelValue) {
            loadSendformDetail();
          }
        },
        { immediate: true }
      );

      // 监听对话框显示状态
      watch(
        () => props.modelValue,
        (newVisible) => {
          if (newVisible && props.sendnum) {
            loadSendformDetail();
          }
        }
      );

      /** 图片上传成功处理 */
      const onImageUploadSuccess = (data) => {
        console.log('图片上传成功:', data);
        // 图片数据已经通过 v-model 自动更新到 form.coinImages
      };

      /** 图片删除成功处理 */
      const onImageRemoveSuccess = (data) => {
        console.log('图片删除成功:', data);
        // 图片数据已经通过 v-model 自动更新到 form.coinImages
      };

      // 码表选项
      const editionOptions = getEditionOptions();
      const gradeScoreOptions = GRADE_SCORE_OPTIONS;
      const specialMarkOptions = SPECIAL_MARK_OPTIONS;
      const authenticityOptions = getAuthenticityOptions();
      const coinRemarkOptions = COIN_REMARK_OPTIONS;
      const starLevelOptions = STAR_LEVEL_OPTIONS;
      const boxTypeOptions = BOX_TYPE_OPTIONS;

      // 表单数据
      const form = reactive({
        coinType: 'banknote', // 默认纸币
        diyCode: '',
        coinName1: '',
        coinName2: '',
        coinVersion: '', // 版别
        yearInfo: '', // 年代
        serialNumber: '', // 编号
        bankName: '', // 银行名称
        region: '', // 地区
        faceVal: '', // 面值
        coinSize: '', // 尺寸
        coinWeight: null, // 重量
        material: '', // 材质
        boxType: '', // 盒子类型
        catalog: '', // 目录
        authenticity: '', // 真伪
        coinRemark: '', // 钱币备注
        gradeScore: '', // 品相分数
        specialMarks: [], // 特殊标记（多选）
        starLevel: '', // 星级
        internalNote: '', // 对内备注
        externalNote: '', // 对外备注
        standardPrice: null, // 标准价
        internationalPrice: null, // 国际价
        gradeFee: null, // 评级费
        discount: null, // 折扣
        boxFee: null, // 盒子费
        quantity: 1 // 数量
      });

      /** 获取送评单详情并确定钱币类型 */
      const loadSendformDetail = async () => {
        if (!sendnum.value) return;

        try {
          loading.value = true;
          const data = await getSendformDetail(sendnum.value);

          // 合并所有钱币
          const allCoins = [
            ...(data.ancientCoins || []),
            ...(data.machineCoins || []),
            ...(data.silverIngots || []),
            ...(data.banknotes || [])
          ];

          // 确定钱币类型：取第一个钱币的类型作为固定类型
          if (allCoins.length > 0) {
            const firstCoinType = allCoins[0].coinType;
            existingCoinType.value = firstCoinType;

            // 设置表单的钱币类型
            form.coinType = firstCoinType;
          } else {
            // 如果没有钱币，默认为纸币
            existingCoinType.value = 'banknote';
            form.coinType = 'banknote';
          }
        } catch (error) {
          console.error('获取送评单详情失败:', error);
          ElMessage.error('获取送评单信息失败');
          // 默认设置为纸币
          existingCoinType.value = 'banknote';
          form.coinType = 'banknote';
        } finally {
          loading.value = false;
        }
      };

      // 关闭对话框
      const handleClose = () => {
        emit('update:modelValue', false);
        resetForm();
      };

      // 重置表单
      const resetForm = () => {
        Object.keys(form).forEach((key) => {
          if (key === 'coinType') {
            // 钱币类型保持不变
            form[key] = existingCoinType.value || 'banknote';
          } else if (Array.isArray(form[key])) {
            form[key] = [];
          } else if (typeof form[key] === 'number') {
            form[key] = key === 'quantity' ? 1 : null;
          } else {
            form[key] = '';
          }
        });
      };

      // 重置按钮
      const handleReset = () => {
        resetForm();
      };

      // 确认添加
      const handleConfirm = async () => {
        try {
          saving.value = true;

          // 构建钱币数据 - 映射到数据库字段
          const coinData = {
            coinType: form.coinType,
            diyCode: form.diyCode,
            coinName1: form.coinName1,
            coinName2: form.coinName2,
            coinVersion: form.coinVersion,
            year: form.yearInfo,
            yearInfo: form.yearInfo,
            faceValue: form.faceVal,
            faceVal: form.faceVal,
            material: form.material,
            coinSize: form.coinSize,
            coinWeight: form.coinWeight,
            boxType: form.boxType,
            quantity: form.quantity,
            standardPrice: form.standardPrice || 0,
            internationalPrice: form.internationalPrice || 0,
            gradeFee: form.gradeFee || 0,
            discount: form.discount || 0,
            boxFee: form.boxFee || 0,
            authenticity: form.authenticity,
            gradeScore: form.gradeScore,
            specialMark: form.specialMarks.join(','), // 多选转换为逗号分隔字符串
            internalNote: form.internalNote,
            externalNote: form.externalNote,
            catalog: form.catalog,
            bankName: form.bankName,
            region: form.region,
            inspectionNote: form.coinRemark,
            remark: form.internalNote
          };

          await addCoins(sendnum.value, [coinData]);

          ElMessage.success('添加钱币成功');
          emit('confirm');
          emit('done');
          handleClose();
        } catch (error) {
          console.error('添加钱币失败:', error);
          ElMessage.error(error.message || '添加钱币失败');
        } finally {
          saving.value = false;
        }
      };

      return {
        formRef,
        visible,
        saving,
        sendnum,
        form,
        loading,
        existingCoinType,
        coinTypeDisplay,
        // 码表选项
        editionOptions,
        gradeScoreOptions,
        specialMarkOptions,
        authenticityOptions,
        coinRemarkOptions,
        starLevelOptions,
        boxTypeOptions,
        // 方法
        loadSendformDetail,
        handleClose,
        handleReset,
        handleConfirm,
        onImageUploadSuccess,
        onImageRemoveSuccess
      };
    }
  });
</script>

<style scoped>
  .dialog-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .sendform-info {
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }

  .sendform-info h4 {
    margin: 0;
    color: #e74c3c;
    font-size: 16px;
  }

  .section-title {
    margin: 20px 0 10px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .section-title h5 {
    margin: 0;
    color: #303133;
    font-size: 14px;
    font-weight: 600;
  }

  .image-upload {
    min-height: 100px;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  :deep(.el-form-item__label) {
    font-size: 12px;
  }

  :deep(.el-input__inner) {
    font-size: 12px;
  }

  :deep(.el-select) {
    width: 100%;
  }

  :deep(.el-select .el-select__tags) {
    max-width: calc(100% - 30px);
  }
</style>
