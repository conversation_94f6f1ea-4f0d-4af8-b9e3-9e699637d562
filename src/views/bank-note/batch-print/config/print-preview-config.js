/**
 * 打印预览配置文件
 * 用于确保预览与实际打印效果一致
 * @since 2025-01-13
 */

/**
 * 纸张类型与每页标签数的映射配置
 * 基于实际打印测试结果
 */
export const PAPER_LABELS_CONFIG = {
  // A4纸张 (210×297mm)
  A4: {
    portrait: {
      labelsPerPage: 12, // 纵向每页11个标签
      labelSpacing: 5, // 标签间距5px
      pageMargin: 40 // 页边距40px
    },
    landscape: {
      labelsPerPage: 8, // 横向每页8个标签
      labelSpacing: 8,
      pageMargin: 30
    }
  },

  // A5纸张 (148×210mm)
  A5: {
    portrait: {
      labelsPerPage: 7,
      labelSpacing: 4,
      pageMargin: 30
    },
    landscape: {
      labelsPerPage: 5,
      labelSpacing: 6,
      pageMargin: 25
    }
  },

  // 自定义大签纸张
  LARGE_LABEL: {
    portrait: {
      labelsPerPage: 15, // 大签纸每页可放更多标签
      labelSpacing: 2,
      pageMargin: 20
    }
  },

  // 默认配置
  DEFAULT: {
    portrait: {
      labelsPerPage: 10,
      labelSpacing: 5,
      pageMargin: 40
    }
  }
};

/**
 * 根据纸张尺寸检测纸张类型
 * @param {number} width 宽度(mm)
 * @param {number} height 高度(mm)
 * @returns {Object} 纸张配置
 */
export function detectPaperConfig(width, height) {
  const orientation = width <= height ? 'portrait' : 'landscape';

  // A4纸张检测 (允许1mm误差)
  if (Math.abs(width - 210) <= 1 && Math.abs(height - 297) <= 1) {
    return {
      paperType: 'A4',
      orientation,
      config: PAPER_LABELS_CONFIG.A4[orientation]
    };
  }

  // A5纸张检测
  if (Math.abs(width - 148) <= 1 && Math.abs(height - 210) <= 1) {
    return {
      paperType: 'A5',
      orientation,
      config: PAPER_LABELS_CONFIG.A5[orientation]
    };
  }

  // A4横向检测
  if (Math.abs(width - 297) <= 1 && Math.abs(height - 210) <= 1) {
    return {
      paperType: 'A4',
      orientation: 'landscape',
      config: PAPER_LABELS_CONFIG.A4.landscape
    };
  }

  // 大签纸张检测 (宽度>250mm或高度>350mm)
  if (width > 250 || height > 350) {
    return {
      paperType: 'LARGE_LABEL',
      orientation,
      config: PAPER_LABELS_CONFIG.LARGE_LABEL.portrait
    };
  }

  // 默认配置
  return {
    paperType: 'DEFAULT',
    orientation,
    config: PAPER_LABELS_CONFIG.DEFAULT.portrait
  };
}

/**
 * 计算最优的每页标签数
 * @param {number} paperWidth 纸张宽度(mm)
 * @param {number} paperHeight 纸张高度(mm)
 * @param {number} labelHeight 标签高度(px)
 * @param {number} labelSpacing 标签间距(px)
 * @returns {number} 每页标签数
 */
export function calculateOptimalLabelsPerPage(
  paperWidth,
  paperHeight,
  labelHeight = 60,
  labelSpacing = 5
) {
  const paperConfig = detectPaperConfig(paperWidth, paperHeight);

  // 使用预配置的值作为基础
  let baseLabelsPerPage = paperConfig.config.labelsPerPage;

  // 根据实际标签高度进行微调
  const availableHeightPx =
    paperHeight * 3.78 - paperConfig.config.pageMargin * 2; // 转换为px并减去边距
  const labelTotalHeight = labelHeight + labelSpacing;
  const calculatedLabelsPerPage = Math.floor(
    availableHeightPx / labelTotalHeight
  );

  // 取预配置值和计算值的较小者，确保不会超出页面
  const finalLabelsPerPage = Math.min(
    baseLabelsPerPage,
    calculatedLabelsPerPage
  );

  console.log(`=== 标签数计算 ===`);
  console.log(
    `纸张: ${paperConfig.paperType} (${paperWidth}×${paperHeight}mm)`
  );
  console.log(`预配置每页标签数: ${baseLabelsPerPage}`);
  console.log(`计算每页标签数: ${calculatedLabelsPerPage}`);
  console.log(`最终每页标签数: ${finalLabelsPerPage}`);

  return Math.max(1, finalLabelsPerPage); // 至少1个标签
}

/**
 * 获取与实际打印一致的预览配置
 * @param {Object} templatePaperInfo 模板纸张信息
 * @param {number} actualLabelHeight 实际标签高度
 * @returns {Object} 预览配置
 */
export function getConsistentPreviewConfig(
  templatePaperInfo,
  actualLabelHeight = 60
) {
  const paperWidth = templatePaperInfo.width || 210;
  const paperHeight = templatePaperInfo.height || 297;

  const paperConfig = detectPaperConfig(paperWidth, paperHeight);
  const labelsPerPage = calculateOptimalLabelsPerPage(
    paperWidth,
    paperHeight,
    actualLabelHeight
  );

  return {
    paperType: paperConfig.paperType,
    orientation: paperConfig.orientation,
    labelsPerPage,
    labelSpacing: paperConfig.config.labelSpacing,
    pageMargin: paperConfig.config.pageMargin,
    actualLabelHeight,
    paperWidth,
    paperHeight
  };
}

/**
 * 预览与打印一致性验证
 * @param {Object} previewConfig 预览配置
 * @param {Object} printConfig 打印配置
 * @returns {Object} 验证结果
 */
export function validatePreviewPrintConsistency(previewConfig, printConfig) {
  const issues = [];

  // 检查每页标签数
  if (previewConfig.labelsPerPage !== printConfig.labelsPerPage) {
    issues.push(
      `每页标签数不一致: 预览${previewConfig.labelsPerPage} vs 打印${printConfig.labelsPerPage}`
    );
  }

  // 检查标签间距
  if (Math.abs(previewConfig.labelSpacing - printConfig.labelSpacing) > 2) {
    issues.push(
      `标签间距差异过大: 预览${previewConfig.labelSpacing}px vs 打印${printConfig.labelSpacing}px`
    );
  }

  // 检查纸张尺寸
  if (
    Math.abs(previewConfig.paperWidth - printConfig.width) > 5 ||
    Math.abs(previewConfig.paperHeight - printConfig.height) > 5
  ) {
    issues.push(
      `纸张尺寸不一致: 预览${previewConfig.paperWidth}×${previewConfig.paperHeight}mm vs 打印${printConfig.width}×${printConfig.height}mm`
    );
  }

  return {
    isConsistent: issues.length === 0,
    issues,
    recommendation:
      issues.length > 0
        ? '建议调整预览配置以匹配打印设置'
        : '预览与打印配置一致'
  };
}

export default {
  PAPER_LABELS_CONFIG,
  detectPaperConfig,
  calculateOptimalLabelsPerPage,
  getConsistentPreviewConfig,
  validatePreviewPrintConsistency
};
