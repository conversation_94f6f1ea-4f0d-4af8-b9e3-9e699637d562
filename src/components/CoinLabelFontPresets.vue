<template>
  <div class="coin-label-font-presets">
    <div class="presets-header">
      <h4>钱币标签字体预设</h4>
      <el-button size="small" @click="showPreview = !showPreview">
        {{ showPreview ? '隐藏' : '显示' }}预览
      </el-button>
    </div>

    <div v-if="showPreview" class="preview-section">
      <div class="preview-label">
        <div class="title-text" :style="presets.title"> 中国人民银行 </div>
        <div class="denomination-text" :style="presets.denomination">
          1980年贰角
        </div>
        <div class="serial-text" :style="presets.serialNumber">
          S/N FZ-07477995 - 民族人物头像
        </div>
        <div class="grade-section">
          <div class="grade-number" :style="presets.grade"> 68 </div>
          <div class="grade-description" :style="presets.description">
            Superb Gem Unc
          </div>
        </div>
      </div>
    </div>

    <div class="presets-list">
      <div class="preset-item" v-for="(preset, key) in presets" :key="key">
        <div class="preset-info">
          <span class="preset-name">{{ presetNames[key] }}</span>
          <span class="preset-font">{{ preset.fontFamily }}</span>
        </div>
        <div class="preset-actions">
          <el-button
            size="small"
            type="primary"
            @click="applyPreset(key, preset)"
          >
            应用
          </el-button>
          <el-button size="small" @click="copyPreset(preset)">
            复制样式
          </el-button>
        </div>
      </div>
    </div>

    <div class="custom-section">
      <h5>自定义字体组合</h5>
      <el-form :model="customFont" label-width="80px" size="small">
        <el-form-item label="字体">
          <el-select v-model="customFont.fontFamily" placeholder="选择字体">
            <el-option-group
              v-for="(group, groupKey) in fontGroups"
              :key="groupKey"
              :label="group.title"
            >
              <el-option
                v-for="font in group.fonts"
                :key="font.value"
                :label="font.title"
                :value="font.value"
                :style="{ fontFamily: font.value }"
              />
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="大小">
          <el-input-number
            v-model="customFont.fontSize"
            :min="8"
            :max="72"
            :step="1"
          />
          <span style="margin-left: 5px">pt</span>
        </el-form-item>
        <el-form-item label="粗细">
          <el-select v-model="customFont.fontWeight">
            <el-option label="正常" value="normal" />
            <el-option label="粗体" value="bold" />
            <el-option label="更粗" value="bolder" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="applyCustomFont"
            >应用自定义</el-button
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { ElMessage } from 'element-plus';

  const emit = defineEmits(['apply-font', 'copy-style']);

  const showPreview = ref(true);

  // 字体预设配置
  const presets = reactive({
    title: {
      fontFamily: 'STKaiti, KaiTi, serif',
      fontSize: '16pt',
      fontWeight: 'normal',
      color: '#333'
    },
    denomination: {
      fontFamily: 'STKaiti, KaiTi, serif',
      fontSize: '12pt',
      fontWeight: 'normal',
      color: '#333'
    },
    serialNumber: {
      fontFamily: 'Courier New, Monaco, monospace',
      fontSize: '10pt',
      fontWeight: 'normal',
      color: '#666'
    },
    grade: {
      fontFamily: 'Arial, sans-serif',
      fontSize: '24pt',
      fontWeight: 'bold',
      color: '#333'
    },
    description: {
      fontFamily: 'Arial, sans-serif',
      fontSize: '10pt',
      fontWeight: 'normal',
      color: '#666'
    }
  });

  // 预设名称
  const presetNames = {
    title: '标题字体',
    denomination: '面额字体',
    serialNumber: '编号字体',
    grade: '评级字体',
    description: '描述字体'
  };

  // 字体分组
  const fontGroups = {
    chinese: {
      title: '中文字体',
      fonts: [
        { title: '华文楷体', value: 'STKaiti' },
        { title: '楷体', value: 'KaiTi' },
        { title: '华文仿宋', value: 'STFangsong' },
        { title: '仿宋', value: 'FangSong' },
        { title: '宋体', value: 'SimSun' },
        { title: '微软雅黑', value: 'Microsoft YaHei' },
        { title: '黑体', value: 'STHeitiSC-Light' }
      ]
    },
    english: {
      title: '英文字体',
      fonts: [
        { title: 'Arial', value: 'Arial' },
        { title: 'Times New Roman', value: 'Times New Roman' },
        { title: 'Helvetica', value: 'Helvetica' },
        { title: 'Georgia', value: 'Georgia' }
      ]
    },
    monospace: {
      title: '等宽字体',
      fonts: [
        { title: 'Courier New', value: 'Courier New' },
        { title: 'Monaco', value: 'Monaco' },
        { title: 'Consolas', value: 'Consolas' }
      ]
    }
  };

  // 自定义字体配置
  const customFont = reactive({
    fontFamily: 'STKaiti',
    fontSize: 14,
    fontWeight: 'normal'
  });

  // 应用预设
  const applyPreset = (key, preset) => {
    emit('apply-font', {
      type: key,
      style: preset
    });
    ElMessage.success(`已应用${presetNames[key]}样式`);
  };

  // 复制样式
  const copyPreset = (preset) => {
    const styleText = `font-family: ${preset.fontFamily}; font-size: ${preset.fontSize}; font-weight: ${preset.fontWeight};`;

    if (navigator.clipboard) {
      navigator.clipboard.writeText(styleText).then(() => {
        ElMessage.success('样式已复制到剪贴板');
      });
    } else {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = styleText;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      ElMessage.success('样式已复制到剪贴板');
    }

    emit('copy-style', preset);
  };

  // 应用自定义字体
  const applyCustomFont = () => {
    const customStyle = {
      fontFamily: customFont.fontFamily,
      fontSize: customFont.fontSize + 'pt',
      fontWeight: customFont.fontWeight
    };

    emit('apply-font', {
      type: 'custom',
      style: customStyle
    });

    ElMessage.success('已应用自定义字体样式');
  };
</script>

<style scoped>
  .coin-label-font-presets {
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
  }

  .presets-header {
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .presets-header h4 {
    margin: 0;
    font-size: 14px;
    color: #333;
  }

  .preview-section {
    padding: 16px;
    background: #fff;
    border-bottom: 1px solid #eee;
  }

  .preview-label {
    border: 2px solid #ddd;
    border-radius: 4px;
    padding: 16px;
    background: #fafafa;
    text-align: center;
  }

  .title-text {
    margin-bottom: 8px;
  }

  .denomination-text {
    margin-bottom: 12px;
  }

  .serial-text {
    margin-bottom: 16px;
  }

  .grade-section {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
  }

  .presets-list {
    padding: 16px;
  }

  .preset-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .preset-item:last-child {
    border-bottom: none;
  }

  .preset-info {
    flex: 1;
  }

  .preset-name {
    font-weight: 500;
    color: #333;
    margin-right: 8px;
  }

  .preset-font {
    font-size: 12px;
    color: #666;
  }

  .preset-actions {
    display: flex;
    gap: 8px;
  }

  .custom-section {
    padding: 16px;
    background: #f8f9fa;
    border-top: 1px solid #eee;
  }

  .custom-section h5 {
    margin: 0 0 12px 0;
    font-size: 13px;
    color: #333;
  }
</style>
