/**
 * hiprint 增强字体选择器
 * 支持字体预览和分类显示
 */

export default function createFontSelector() {
  function FontSelector() {
    this.name = 'fontFamily';
  }

  // 字体分类配置
  const fontCategories = {
    chinese: {
      title: '中文字体',
      fonts: [
        { title: '微软雅黑', value: 'Microsoft YaHei' },
        { title: '宋体', value: 'SimSun' },
        { title: '黑体', value: 'STHeitiSC-Light' },
        { title: '华文楷体', value: 'STKaiti' },
        { title: '楷体', value: 'KaiTi' },
        { title: '楷体GB2312', value: 'KaiTi_GB2312' },
        { title: '华文仿宋', value: 'STFangsong' },
        { title: '仿宋', value: 'FangSong' },
        { title: '仿宋GB2312', value: 'FangSong_GB2312' },
        { title: '华文中宋', value: 'STZhongsong' },
        { title: '华文新魏', value: 'STXinwei' }
      ]
    },
    english: {
      title: '英文字体',
      fonts: [
        { title: 'Arial', value: 'Arial' },
        { title: 'Arial Bold', value: 'Arial, sans-serif; font-weight: bold' },
        { title: 'Times New Roman', value: 'Times New Roman' },
        { title: 'Helvetica', value: 'Helvetica' },
        { title: 'Georgia', value: 'Georgia' }
      ]
    },
    monospace: {
      title: '等宽字体',
      fonts: [
        { title: 'Courier New', value: 'Courier New' },
        { title: 'Monaco', value: 'Monaco' },
        { title: 'Consolas', value: 'Consolas' }
      ]
    }
  };

  FontSelector.prototype.css = function (element, value) {
    if (element && element.length) {
      if (value) {
        element.css('font-family', value);
        return 'font-family:' + value;
      }
      element[0].style.fontFamily = '';
    }
    return null;
  };

  FontSelector.prototype.createTarget = function () {
    // 创建所有字体选项
    let allFonts = [];
    Object.values(fontCategories).forEach((category) => {
      allFonts.push(...category.fonts);
    });

    // 生成选项HTML
    let optionsHtml = '<option value="">默认字体</option>';

    // 按分类生成选项组
    Object.entries(fontCategories).forEach(([key, category]) => {
      optionsHtml += `<optgroup label="${category.title}">`;
      category.fonts.forEach((font) => {
        optionsHtml += `<option value="${font.value}" style="font-family: ${font.value}">${font.title}</option>`;
      });
      optionsHtml += '</optgroup>';
    });

    this.target = $(`
      <div class="hiprint-option-item">
        <div class="hiprint-option-item-label">字体</div>
        <div class="hiprint-option-item-field">
          <select class="auto-submit font-family-select" style="width: 100%;">
            ${optionsHtml}
          </select>
          <div class="font-preview-text" style="
            margin-top: 8px; 
            padding: 6px; 
            border: 1px solid #eee; 
            border-radius: 3px; 
            background: #f9f9f9;
            font-size: 12px;
            min-height: 20px;
            color: #666;
          ">
            预览: 中国人民银行 ABC123
          </div>
        </div>
      </div>
    `);

    const selectElement = this.target.find('.font-family-select');
    const previewElement = this.target.find('.font-preview-text');

    // 字体选择事件
    selectElement.on('change', function () {
      const selectedFont = $(this).val();
      const selectedText = $(this).find('option:selected').text();

      if (selectedFont) {
        previewElement.css('font-family', selectedFont);
        previewElement.css('color', '#333');
      } else {
        previewElement.css('font-family', '');
        previewElement.css('color', '#666');
      }
    });

    return this.target;
  };

  FontSelector.prototype.getValue = function () {
    return this.target.find('.font-family-select').val();
  };

  FontSelector.prototype.setValue = function (value) {
    if (value) {
      const selectElement = this.target.find('.font-family-select');
      const previewElement = this.target.find('.font-preview-text');

      selectElement.val(value);
      previewElement.css('font-family', value);
      previewElement.css('color', '#333');
    }
  };

  FontSelector.prototype.destroy = function () {
    this.target.remove();
  };

  return FontSelector;
}

/**
 * 应用增强字体选择器到hiprint
 */
export function applyEnhancedFontSelector(hiprint) {
  if (!hiprint) {
    console.warn('hiprint实例不存在，无法应用增强字体选择器');
    return;
  }

  try {
    // 获取现有配置
    const currentConfig = hiprint.getConfig() || {};
    const currentOptionItems = currentConfig.optionItems || [];

    // 添加增强字体选择器
    const enhancedOptionItems = [...currentOptionItems, createFontSelector()];

    // 应用配置
    hiprint.setConfig({
      optionItems: enhancedOptionItems
    });

    console.log('增强字体选择器已应用');
  } catch (error) {
    console.error('应用增强字体选择器失败:', error);
  }
}

/**
 * 获取推荐的钱币标签字体配置
 */
export function getCoinLabelFontConfig() {
  return {
    // 标题字体（如"中国人民银行"）
    title: {
      fontFamily: 'STKaiti, KaiTi, serif',
      fontSize: '16pt',
      fontWeight: 'normal'
    },
    // 年份和面额（如"1980年贰角"）
    denomination: {
      fontFamily: 'STKaiti, KaiTi, serif',
      fontSize: '12pt',
      fontWeight: 'normal'
    },
    // 编号（如"S/N FZ-07477995"）
    serialNumber: {
      fontFamily: 'Courier New, Monaco, monospace',
      fontSize: '10pt',
      fontWeight: 'normal'
    },
    // 评级分数（如"68"）
    grade: {
      fontFamily: 'Arial, sans-serif',
      fontSize: '24pt',
      fontWeight: 'bold'
    },
    // 英文描述（如"Superb Gem Unc"）
    description: {
      fontFamily: 'Arial, sans-serif',
      fontSize: '10pt',
      fontWeight: 'normal'
    }
  };
}
