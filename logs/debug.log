[2m2025-08-04 15:43:40.640[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[kground-preinit][0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m HV000001: Hibernate Validator 8.0.2.Final
[2m2025-08-04 15:43:41.082[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Starting App using Java 17.0.15 with PID 85575 (/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes started by paynexc in /Users/<USER>/project/ele-admin)
[2m2025-08-04 15:43:41.083[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m Running with Spring Boot v3.5.3, Spring v6.2.8
[2m2025-08-04 15:43:41.083[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mcom.payne.App                           [0;39m [2m:[0;39m The following 1 profile is active: "dev"
[2m2025-08-04 15:43:41.173[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.devtools.restart.ChangeableUrls   [0;39m [2m:[0;39m The Class-Path manifest attribute in /Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/ojdbc8-23.2.0.0.0.jar referenced one or more files that do not exist: file:/Users/<USER>/.m2/repository/com/oracle/ojdbc8/23.2.0.0.0/oraclepki.jar
[2m2025-08-04 15:43:41.174[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
[2m2025-08-04 15:43:41.174[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.e.DevToolsPropertyDefaultsPostProcessor[0;39m [2m:[0;39m For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
[2m2025-08-04 15:43:42.782[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 15:43:42.788[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data JPA repositories in DEFAULT mode.
[2m2025-08-04 15:43:42.861[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 55 ms. Found 0 JPA repository interfaces.
[2m2025-08-04 15:43:42.873[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 15:43:42.891[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
[2m2025-08-04 15:43:42.909[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 16 ms. Found 0 MongoDB repository interfaces.
[2m2025-08-04 15:43:42.926[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Multiple Spring Data modules found, entering strict repository configuration mode
[2m2025-08-04 15:43:42.931[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2m2025-08-04 15:43:42.962[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36m.s.d.r.c.RepositoryConfigurationDelegate[0;39m [2m:[0;39m Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
[2m2025-08-04 15:43:44.772[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.s.b.w.embedded.tomcat.TomcatWebServer [0;39m [2m:[0;39m Tomcat initialized with port 7070 (http)
[2m2025-08-04 15:43:44.823[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m Initializing ProtocolHandler ["http-nio-7070"]
[2m2025-08-04 15:43:44.830[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m Starting service [Tomcat]
[2m2025-08-04 15:43:44.830[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.apache.catalina.core.StandardEngine   [0;39m [2m:[0;39m Starting Servlet engine: [Apache Tomcat/10.1.42]
[2m2025-08-04 15:43:45.010[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m Initializing Spring embedded WebApplicationContext
[2m2025-08-04 15:43:45.011[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mw.s.c.ServletWebServerApplicationContext[0;39m [2m:[0;39m Root WebApplicationContext: initialization completed in 3836 ms
[2m2025-08-04 15:43:45.675[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.mongodb.driver.client               [0;39m [2m:[0;39m MongoClient with metadata {"driver": {"name": "mongo-java-driver|sync|spring-boot", "version": "5.5.1"}, "os": {"type": "Darwin", "name": "Mac OS X", "architecture": "aarch64", "version": "15.5"}, "platform": "Java/Azul Systems, Inc./17.0.15+6-LTS"} created with settings MongoClientSettings{readPreference=primary, writeConcern=WriteConcern{w=null, wTimeout=null ms, journal=null}, retryWrites=true, retryReads=true, readConcern=ReadConcern{level=null}, credential=MongoCredential{mechanism=null, userName='mongo_txRpa7', source='admin', password=<hidden>, mechanismProperties=<hidden>}, transportSettings=null, commandListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsCommandListener@420dee82], codecRegistry=ProvidersCodecRegistry{codecProviders=[ValueCodecProvider{}, BsonValueCodecProvider{}, DBRefCodecProvider{}, DBObjectCodecProvider{}, DocumentCodecProvider{}, CollectionCodecProvider{}, IterableCodecProvider{}, MapCodecProvider{}, GeoJsonCodecProvider{}, GridFSFileCodecProvider{}, Jsr310CodecProvider{}, JsonObjectCodecProvider{}, BsonCodecProvider{}, EnumCodecProvider{}, com.mongodb.client.model.mql.ExpressionCodecProvider@930b6f9, com.mongodb.Jep395RecordCodecProvider@15363eba, com.mongodb.KotlinCodecProvider@6d6e8dfc]}, loggerSettings=LoggerSettings{maxDocumentLength=1000}, clusterSettings={hosts=[paynexc.home:27017], srvServiceName=mongodb, mode=SINGLE, requiredClusterType=UNKNOWN, requiredReplicaSetName='null', serverSelector='null', clusterListeners='[]', serverSelectionTimeout='30000 ms', localThreshold='15 ms'}, socketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=0, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, heartbeatSocketSettings=SocketSettings{connectTimeoutMS=10000, readTimeoutMS=10000, receiveBufferSize=0, proxySettings=ProxySettings{host=null, port=null, username=null, password=null}}, connectionPoolSettings=ConnectionPoolSettings{maxSize=100, minSize=0, maxWaitTimeMS=120000, maxConnectionLifeTimeMS=0, maxConnectionIdleTimeMS=0, maintenanceInitialDelayMS=0, maintenanceFrequencyMS=60000, connectionPoolListeners=[io.micrometer.core.instrument.binder.mongodb.MongoMetricsConnectionPoolListener@5c4c4e96], maxConnecting=2}, serverSettings=ServerSettings{heartbeatFrequencyMS=10000, minHeartbeatFrequencyMS=500, serverMonitoringMode=AUTO, serverListeners='[]', serverMonitorListeners='[]'}, sslSettings=SslSettings{enabled=false, invalidHostNameAllowed=false, context=null}, applicationName='null', compressorList=[], uuidRepresentation=JAVA_LEGACY, serverApi=null, autoEncryptionSettings=null, dnsClient=null, inetAddressResolver=null, contextProvider=null, timeoutMS=null}
[2m2025-08-04 15:43:45.957[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[nexc.home:27017][0;39m [36morg.mongodb.driver.cluster              [0;39m [2m:[0;39m Monitor thread successfully connected to server with description ServerDescription{address=paynexc.home:27017, type=STANDALONE, cryptd=false, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=21, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=154033000, minRoundTripTimeNanos=0}
[2m2025-08-04 15:43:46.121[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join properties config complete
[2m2025-08-04 15:43:46.340[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.g.y.a.MybatisPlusJoinAutoConfiguration[0;39m [2m:[0;39m mybatis plus join SqlInjector init
[2m2025-08-04 15:43:46.345[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.payne.core.handler.DecryptInterceptor@1fae279e'
[2m2025-08-04 15:43:46.345[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@30e787e2'
[2m2025-08-04 15:43:46.345[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Registered plugin: 'com.github.yulichang.interceptor.MPJInterceptor@26be5ee'
[2m2025-08-04 15:43:46.734[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/ColorLabelConfigMapper.xml]'
[2m2025-08-04 15:43:46.812[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/PjOSendformItemMapper.xml]'
[2m2025-08-04 15:43:46.869[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-server/target/classes/com/payne/server/banknote/mapper/xml/PjOSendformMapper.xml]'
[2m2025-08-04 15:43:46.916[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/code/mapper/xml/CodeCommonMapper.xml]'
[2m2025-08-04 15:43:46.951[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/code/mapper/xml/CodeTypeMapper.xml]'
[2m2025-08-04 15:43:46.988[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/LoginRecordMapper.xml]'
[2m2025-08-04 15:43:47.015[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/RoleMenuMapper.xml]'
[2m2025-08-04 15:43:47.048[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysAccountMapper.xml]'
[2m2025-08-04 15:43:47.075[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysAccountRoleMapper.xml]'
[2m2025-08-04 15:43:47.107[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysMenuMapper.xml]'
[2m2025-08-04 15:43:47.134[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysParamMapper.xml]'
[2m2025-08-04 15:43:47.156[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysRoleMapper.xml]'
[2m2025-08-04 15:43:47.178[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/SysRoleScopeMapper.xml]'
[2m2025-08-04 15:43:47.213[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.m.e.s.MybatisSqlSessionFactoryBean  [0;39m [2m:[0;39m Parsed mapper file: 'file [/Users/<USER>/project/ele-admin/dev-platform/payne-upms/target/classes/com/payne/upms/system/mapper/xml/UserInfoMapper.xml]'
[2m2025-08-04 15:43:47.224[0;39m [32mDEBUG[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.b.mybatisplus.core.toolkit.Sequence   [0;39m [2m:[0;39m Initialization Sequence datacenterId:0 workerId:6
[2m2025-08-04 15:43:47.772[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m Redisson 3.36.0
[2m2025-08-04 15:43:47.978[0;39m [31mERROR[0;39m [35m85575[0;39m [2m---[0;39m [2m[           main][0;39m [36mi.n.r.d.DnsServerAddressStreamProviders [0;39m [2m:[0;39m Unable to load io.netty.resolver.dns.macos.MacOSDnsServerAddressStreamProvider, fallback to system defaults. This may result in incorrect DNS resolutions on MacOS. Check whether you have a dependency on 'io.netty:netty-resolver-dns-native-macos'. Use DEBUG level to see the full stack: java.lang.UnsatisfiedLinkError: failed to load the required native library
[2m2025-08-04 15:43:49.040[0;39m [32m INFO[0;39m [35m85575[0;39m [2m---[0;39m [2m[isson-netty-1-7][0;39m [36mo.redisson.connection.ConnectionsHolder [0;39m [2m:[0;39m 1 connections initialized for paynexc.home/************:6379
